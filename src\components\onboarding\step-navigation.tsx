import { Box, Button } from '@mui/material';
import { <PERSON><PERSON><PERSON>, <PERSON>For<PERSON>, SkipNext } from '@mui/icons-material';
import { PrimaryButton } from '@/components/ui';

interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onBack: () => void;
  onSkip?: () => void;
  isNextDisabled?: boolean;
  isLoading?: boolean;
  showSkip?: boolean;
  nextLabel?: string;
  backLabel?: string;
  skipLabel?: string;
}

export default function StepNavigation({
  currentStep,
  totalSteps,
  onNext,
  onBack,
  onSkip,
  isNextDisabled = false,
  isLoading = false,
  showSkip = false,
  nextLabel = "Continue",
  backLabel = "Back",
  skipLabel = "Skip"
}: StepNavigationProps) {
  const progress = ((currentStep + 1) / totalSteps) * 100;
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  return (
    <Box>
      {/* Smooth Progress Bar */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{
          position: 'relative',
          height: 8,
          borderRadius: 8,
          backgroundColor: 'rgba(0, 0, 0, 0.08)',
          overflow: 'hidden',
          boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)'
        }}>
          {/* Progress fill with gradient and animation */}
          <Box sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            height: '100%',
            width: `${progress}%`,
            background: 'linear-gradient(90deg, var(--slate-blue) 0%, var(--grape) 50%, var(--slate-blue) 100%)',
            borderRadius: 8,
            transition: 'width 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
            boxShadow: '0 2px 8px rgba(94, 96, 206, 0.4)',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%)',
              animation: 'shine 2s infinite',
            }
          }} />
        </Box>
      </Box>

      {/* Navigation Buttons */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        gap: 2
      }}>
        {/* Back Button */}
        <Box>
          {!isFirstStep && (
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={onBack}
              disabled={isLoading}
              sx={{
                color: 'var(--slate-blue)',
                borderColor: 'var(--slate-blue)',
                background: 'linear-gradient(135deg, rgba(94, 96, 206, 0.05) 0%, transparent 100%)',
                '&:hover': {
                  borderColor: 'var(--grape)',
                  backgroundColor: 'rgba(94, 96, 206, 0.1)',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 12px rgba(94, 96, 206, 0.2)',
                },
                '&:active': {
                  transform: 'translateY(0px)',
                },
                textTransform: 'none',
                fontWeight: 600,
                borderRadius: 3,
                px: 3,
                py: 1.2,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                borderWidth: 2,
                '&:disabled': {
                  opacity: 0.5,
                }
              }}
            >
              {backLabel}
            </Button>
          )}
        </Box>

        {/* Spacer */}
        <Box sx={{ flex: 1 }} />

        {/* Skip and Continue Buttons */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Skip Button */}
          {showSkip && onSkip && (
            <Button
              variant="text"
              startIcon={<SkipNext />}
              onClick={onSkip}
              disabled={isLoading}
              sx={{
                color: 'text.secondary',
                background: 'linear-gradient(135deg, rgba(0, 0, 0, 0.02) 0%, transparent 100%)',
                '&:hover': {
                  backgroundColor: 'rgba(94, 96, 206, 0.08)',
                  color: 'var(--slate-blue)',
                  transform: 'translateY(-1px)',
                },
                '&:active': {
                  transform: 'translateY(0px)',
                },
                textTransform: 'none',
                fontWeight: 500,
                borderRadius: 3,
                px: 3,
                py: 1.2,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
            >
              {skipLabel}
            </Button>
          )}

          {/* Next/Continue Button */}
          <PrimaryButton
            onClick={onNext}
            loading={isLoading}
            loadingText={isLastStep ? "Completing..." : "Loading..."}
            disabled={isNextDisabled}
            endIcon={!isLastStep ? <ArrowForward /> : undefined}
            sx={{
              minWidth: 140,
              py: 1.2,
              px: 4,
              transform: isNextDisabled ? 'none' : 'translateY(0px)',
              '&:hover': {
                transform: isNextDisabled ? 'none' : 'translateY(-2px)',
                boxShadow: isNextDisabled ? 'none' : '0 6px 20px rgba(94, 96, 206, 0.4)',
              },
              '&:active': {
                transform: isNextDisabled ? 'none' : 'translateY(-1px)',
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            }}
          >
            {isLastStep ? "Complete Setup" : nextLabel}
          </PrimaryButton>
        </Box>
      </Box>
    </Box>
  );
} 