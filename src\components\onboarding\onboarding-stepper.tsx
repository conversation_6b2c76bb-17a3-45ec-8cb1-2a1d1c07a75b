'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Box } from '@mui/material';
import { AuthLayout, AuthPanel } from '@/components/auth';
import StepUserType from './step-user-type';
import StepExperience from './step-experience';
import StepWorkBasic from './step-work-basic';
import StepCompanyStage from './step-company-stage';
import StepWorkDetails from './step-work-details';
import StepProfessionalFocus from './step-professional-focus';
import StepPlatformGoals from './step-platform-goals';
import StepIndustries from './step-industries';
import StepNavigation from './step-navigation';

export interface OnboardingData {
  userType?: string;
  experienceLevel?: string;
  company?: string;
  companyStage?: string;
  companySize?: string;
  workType?: string;
  role?: string;
  goals: string[];
  selectedIndustries: string[];
  referralSource?: string;
}

const TOTAL_STEPS = 8;

export default function OnboardingStepper() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const [formData, setFormData] = useState<OnboardingData>({
    goals: [],
    selectedIndustries: []
  });

  const updateFormData = (updates: Partial<OnboardingData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
    setError(''); // Clear any errors when user makes changes
  };

  // Show loading state during completion
  if (isCompleting) {
    return (
      <AuthLayout>
        <AuthPanel>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            py: 8,
            textAlign: 'center'
          }}>
            <Box>
              <Box sx={{
                width: 40,
                height: 40,
                border: '4px solid rgba(94, 96, 206, 0.1)',
                borderTop: '4px solid var(--slate-blue)',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                mx: 'auto',
                mb: 2
              }} />
              <Box sx={{
                fontSize: '1.1rem',
                fontWeight: 500,
                color: 'text.primary'
              }}>
                Setting up your profile...
              </Box>
              <Box sx={{
                fontSize: '0.9rem',
                color: 'text.secondary',
                mt: 1
              }}>
                Please wait while we personalize your experience
              </Box>
            </Box>
          </Box>
        </AuthPanel>
      </AuthLayout>
    );
  }

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 0: // User Type
        if (!formData.userType) {
          setError('Please select what brings you to Nexus');
          return false;
        }
        return true;

      case 1: // Experience Level
        if (!formData.experienceLevel) {
          setError('Please select your experience level');
          return false;
        }
        return true;

      case 2: // Work Basic
        if (formData.userType === 'founder') {
          return true; // Company name is optional
        } else {
          if (!formData.workType) {
            setError('Please select your work situation');
            return false;
          }
        }
        return true;

      case 3: // Company Stage
        if (formData.userType === 'founder') {
          // Optional for founders
          return true;
        }
        return true; // Skip for non-founders

      case 4: // Work Details (Team Size)
        return true; // Optional step

      case 5: // Professional Focus
        if (!formData.role) {
          setError('Please select your professional role');
          return false;
        }
        return true;

      case 6: // Platform Goals
        if (formData.goals.length === 0) {
          setError('Please select at least one goal');
          return false;
        }
        return true;

      case 7: // Industries
        if (formData.selectedIndustries.length === 0) {
          setError('Please select at least 1 industry');
          return false;
        }
        return true;

      default:
        return true;
    }
  };

  const canSkipCurrentStep = (): boolean => {
    switch (currentStep) {
      case 2: // Work basic - can skip company name for founders
        return formData.userType === 'founder';
      case 3: // Company stage - optional for founders
        return true;
      case 4: // Work details - optional for everyone
        return true;
      default:
        return false;
    }
  };

  const handleNext = async () => {
    if (!validateCurrentStep()) {
      return;
    }

    if (currentStep === TOTAL_STEPS - 1) {
      // Last step - submit data
      await handleSubmit();
    } else {
      setCurrentStep(prev => Math.min(prev + 1, TOTAL_STEPS - 1));
    }
  };

  const handleBack = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
    setError('');
  };

  const handleSkip = () => {
    if (canSkipCurrentStep()) {
      if (currentStep === TOTAL_STEPS - 1) {
        handleSubmit();
      } else {
        setCurrentStep(prev => Math.min(prev + 1, TOTAL_STEPS - 1));
      }
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setIsCompleting(true);

    try {
      const response = await fetch('/api/onboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to complete onboarding');
      }

      // Small delay to show the loading state
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Onboarding completed successfully
      router.push('/');
    } catch (error: any) {
      setError(error.message || 'Failed to save your preferences. Please try again.');
      setIsCompleting(false);
    } finally {
      setIsLoading(false);
    }
  };

  const isNextDisabled = () => {
    switch (currentStep) {
      case 0:
        return !formData.userType;
      case 1:
        return !formData.experienceLevel;
      case 2:
        if (formData.userType === 'founder') {
          return false; // Can skip company name
        }
        return !formData.workType;
      case 3:
        return false; // Optional step
      case 4:
        return false; // Optional step
      case 5:
        return !formData.role;
      case 6:
        return formData.goals.length === 0;
      case 7:
        return formData.selectedIndustries.length === 0;
      default:
        return false;
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <StepUserType
            userType={formData.userType}
            onUserTypeChange={(userType) => updateFormData({ userType })}
          />
        );

      case 1:
        return (
          <StepExperience
            userType={formData.userType}
            experienceLevel={formData.experienceLevel}
            onExperienceLevelChange={(experienceLevel) => updateFormData({ experienceLevel })}
          />
        );

      case 2:
        return (
          <StepWorkBasic
            userType={formData.userType}
            company={formData.company}
            workType={formData.workType}
            onCompanyChange={(company) => updateFormData({ company })}
            onWorkTypeChange={(workType) => updateFormData({ workType })}
          />
        );

      case 3:
        return (
          <StepCompanyStage
            userType={formData.userType}
            companyStage={formData.companyStage}
            onCompanyStageChange={(companyStage) => updateFormData({ companyStage })}
          />
        );

      case 4:
        return (
          <StepWorkDetails
            userType={formData.userType}
            companySize={formData.companySize}
            workType={formData.workType}
            onCompanySizeChange={(companySize) => updateFormData({ companySize })}
          />
        );

      case 5:
        return (
          <StepProfessionalFocus
            role={formData.role}
            onRoleChange={(role) => updateFormData({ role })}
          />
        );

      case 6:
        return (
          <StepPlatformGoals
            goals={formData.goals}
            onGoalsChange={(goals) => updateFormData({ goals })}
          />
        );

      case 7:
        return (
          <StepIndustries
            selectedIndustries={formData.selectedIndustries}
            onIndustriesChange={(selectedIndustries) => updateFormData({ selectedIndustries })}
            error={error}
          />
        );

      default:
        return null;
    }
  };

  return (
    <AuthLayout>
      <AuthPanel maxWidth={900}>
        <Box sx={{ minHeight: '70vh', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ flex: 1, mb: 4 }}>
            {renderCurrentStep()}
          </Box>

          <StepNavigation
            currentStep={currentStep}
            totalSteps={TOTAL_STEPS}
            onNext={handleNext}
            onBack={handleBack}
            onSkip={canSkipCurrentStep() ? handleSkip : undefined}
            isNextDisabled={isNextDisabled()}
            isLoading={isLoading}
            showSkip={canSkipCurrentStep()}
          />
        </Box>
      </AuthPanel>
    </AuthLayout>
  );
} 