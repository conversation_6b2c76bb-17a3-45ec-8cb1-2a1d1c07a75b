import { Box, Chip, Alert } from '@mui/material';
import { Business } from '@mui/icons-material';
import { industries } from '@/config/constant';
import { AuthHeader } from '@/components/auth';
import OptionCard from './option-card';

interface StepIndustriesProps {
  selectedIndustries: string[];
  onIndustriesChange: (industries: string[]) => void;
  error?: string;
}

export default function StepIndustries({
  selectedIndustries,
  onIndustriesChange,
  error
}: StepIndustriesProps) {
  const handleIndustryToggle = (industry: string) => {
    if (selectedIndustries.includes(industry)) {
      onIndustriesChange(selectedIndustries.filter(item => item !== industry));
    } else {
      if (selectedIndustries.length >= 3) {
        return; // Don't add more than 3
      }
      onIndustriesChange([...selectedIndustries, industry]);
    }
  };

  // Convert industries to format compatible with OptionCard
  const industryOptions = industries.map(industry => ({
    id: industry,
    label: industry,
    description: `Focus on ${industry.toLowerCase()} related startups and opportunities`
  }));

  return (
    <Box>
      <AuthHeader
        icon={<Business sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What's your focus area?"
        subtitle="Select the industries that best match your work or interests"
        description="Choose 1-3 options to personalize your experience"
      />

      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
        <Chip
          label={`${selectedIndustries.length}/3 selected`}
          color={selectedIndustries.length > 0 ? 'primary' : 'default'}
          sx={{
            fontWeight: 600,
            backgroundColor: selectedIndustries.length > 0 ? 'var(--slate-blue)' : undefined
          }}
        />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Box sx={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'center',
        alignItems: 'flex-start',
        gap: 1.5,
        px: 2,
        maxHeight: '400px',
        overflowY: 'auto',
        overflowX: 'hidden',
        '&::-webkit-scrollbar': {
          width: '6px',
        },
        '&::-webkit-scrollbar-track': {
          background: 'rgba(0, 0, 0, 0.1)',
          borderRadius: '3px',
        },
        '&::-webkit-scrollbar-thumb': {
          background: 'var(--slate-blue)',
          borderRadius: '3px',
          '&:hover': {
            background: 'var(--grape)',
          },
        },
      }}>
        {industryOptions.map((industry) => (
          <OptionCard
            key={industry.id}
            id={industry.id}
            label={industry.label}
            description={industry.description}
            isSelected={selectedIndustries.includes(industry.id)}
            onClick={handleIndustryToggle}
            variant="compact"
          />
        ))}
      </Box>
    </Box>
  );
} 