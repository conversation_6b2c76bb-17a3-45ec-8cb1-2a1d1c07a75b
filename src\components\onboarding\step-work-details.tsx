import { Box } from '@mui/material';
import { People } from '@mui/icons-material';
import { companySizes } from '@/config/constant';
import OptionCard from './option-card';
import { AuthHeader } from '@/components/auth';

interface StepWorkDetailsProps {
  userType?: string;
  companySize?: string;
  workType?: string;
  onCompanySizeChange: (size: string) => void;
}

export default function StepWorkDetails({
  userType,
  companySize,
  workType,
  onCompanySizeChange
}: StepWorkDetailsProps) {
  const isFounder = userType === 'founder';
  const showOrgSize = !isFounder && workType && ['team', 'agency'].includes(workType);

  if (isFounder) {
    return (
      <Box>
        <AuthHeader
          icon={<People sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
          title="How big is your team?"
          subtitle="Tell us about your startup team"
          description="This helps us understand your current scale and connect you with similar-sized teams"
        />

        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          flexWrap: 'wrap',
          gap: 2,
          px: 2
        }}>
          {companySizes.map((size) => (
            <OptionCard
              key={size.id}
              id={size.id}
              label={size.label}
              description={size.description}
              isSelected={companySize === size.id}
              onClick={onCompanySizeChange}
            />
          ))}
        </Box>
      </Box>
    );
  }

  if (showOrgSize) {
    return (
      <Box>
        <AuthHeader
          icon={<People sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
          title="How big is your organization?"
          subtitle="Tell us about your workplace"
          description="This helps us understand your professional environment"
        />

        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          flexWrap: 'wrap',
          gap: 2,
          px: 2
        }}>
          {companySizes.map((size) => (
            <OptionCard
              key={size.id}
              id={size.id}
              label={size.label}
              description={size.description}
              isSelected={companySize === size.id}
              onClick={onCompanySizeChange}
            />
          ))}
        </Box>
      </Box>
    );
  }

  // Skip this step for other user types
  return (
    <Box>
      <AuthHeader
        icon={<People sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="You're all set for this step!"
        subtitle="Let's continue with your professional focus"
        description="We'll move on to understanding your role and interests"
      />
    </Box>
  );
} 