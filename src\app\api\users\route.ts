import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/config/prisma-client";

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    const users = await prisma.user.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });
    return NextResponse.json(users);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userName, bio, websiteUrl, role, company, accountId } = body;

    const user = await prisma.user.create({
      data: {
        userName,
        bio,
        websiteUrl,
        role,
        company,
        accountId,
      },
    });

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create user" },
      { status: 500 }
    );
  }
} 