import { Card, CardContent, Typography } from '@mui/material';
import { CheckCircle } from '@mui/icons-material';

interface IndustryCardProps {
  industry: string;
  isSelected: boolean;
  onClick: () => void;
}

export default function IndustryCard({ industry, isSelected, onClick }: IndustryCardProps) {
  return (
    <Card
      onClick={onClick}
      sx={{
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        border: '2px solid',
        borderColor: isSelected ? 'var(--slate-blue)' : 'transparent',
        backgroundColor: isSelected ? 'rgba(94, 96, 206, 0.05)' : 'white',
        borderRadius: 3,
        minWidth: 140,
        maxWidth: 140,
        height: 60,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        '&:hover': {
          transform: 'translateY(-1px)',
          boxShadow: 3,
          borderColor: isSelected ? 'var(--slate-blue)' : 'var(--grape)',
        }
      }}
    >
      <CardContent sx={{ p: 1, '&:last-child': { pb: 1 }, textAlign: 'center', width: '100%' }}>
        <Typography 
          variant="body2" 
          sx={{ 
            fontWeight: 500,
            color: isSelected ? 'var(--slate-blue)' : 'text.primary',
            fontSize: '0.75rem',
            lineHeight: 1.2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 0.5,
            height: '100%'
          }}
        >
          {industry.length > 25 ? `${industry.substring(0, 22)}...` : industry}
          {isSelected && (
            <CheckCircle 
              sx={{ 
                color: 'var(--slate-blue)', 
                fontSize: 14,
                flexShrink: 0
              }} 
            />
          )}
        </Typography>
      </CardContent>
    </Card>
  );
}
