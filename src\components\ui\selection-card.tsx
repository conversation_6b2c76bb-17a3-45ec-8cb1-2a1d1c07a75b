import { Card, CardContent, Typography, Box } from '@mui/material';
import { CheckCircle } from '@mui/icons-material';

interface SelectionCardProps {
  id: string;
  label: string;
  description?: string;
  icon?: string;
  isSelected: boolean;
  onClick: (id: string) => void;
  variant?: 'default' | 'compact';
  maxLabelLength?: number;
}

export default function SelectionCard({
  id,
  label,
  description,
  icon,
  isSelected,
  onClick,
  variant = 'default',
  maxLabelLength
}: SelectionCardProps) {
  const isCompact = variant === 'compact';
  
  const displayLabel = maxLabelLength && label.length > maxLabelLength 
    ? `${label.substring(0, maxLabelLength - 3)}...` 
    : label;

  return (
    <Card
      onClick={() => onClick(id)}
      sx={{
        cursor: 'pointer',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        border: '2px solid',
        borderColor: isSelected ? 'var(--slate-blue)' : isCompact ? 'transparent' : '#e0e0e0',
        background: isSelected
          ? 'linear-gradient(135deg, rgba(94, 96, 206, 0.15) 0%, rgba(94, 96, 206, 0.05) 100%)'
          : isCompact 
            ? 'white'
            : 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
        borderRadius: isCompact ? 3 : 4,
        width: isCompact ? 140 : 240,
        minWidth: isCompact ? 140 : 240,
        maxWidth: isCompact ? 140 : 240,
        minHeight: isCompact ? 60 : 120,
        height: isCompact ? 60 : 'auto',
        display: 'flex',
        alignItems: isCompact ? 'center' : 'stretch',
        justifyContent: 'center',
        position: 'relative',
        boxShadow: isSelected
          ? isCompact 
            ? 3
            : '0 8px 25px rgba(94, 96, 206, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1)'
          : isCompact
            ? 'none'
            : '0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.05)',
        '&::before': !isCompact ? {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: isSelected
            ? 'linear-gradient(135deg, rgba(94, 96, 206, 0.1) 0%, transparent 50%)'
            : 'transparent',
          borderRadius: 4,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        } : {},
        '&:hover': {
          transform: isCompact ? 'translateY(-1px)' : 'translateY(-2px) scale(1.02)',
          boxShadow: isSelected
            ? isCompact
              ? 3
              : '0 12px 35px rgba(94, 96, 206, 0.4), 0 6px 15px rgba(0, 0, 0, 0.15)'
            : isCompact
              ? 3
              : '0 8px 25px rgba(94, 96, 206, 0.2), 0 4px 12px rgba(0, 0, 0, 0.12)',
          borderColor: isSelected ? 'var(--slate-blue)' : 'var(--grape)',
          '&::before': !isCompact ? {
            opacity: 1,
          } : {}
        },
        '&:active': {
          transform: isCompact ? 'translateY(0px)' : 'translateY(-1px) scale(1.01)',
        }
      }}
    >
      <CardContent sx={{
        p: isCompact ? 1 : 2.5,
        '&:last-child': { pb: isCompact ? 1 : 2.5 },
        textAlign: 'center',
        width: '100%',
        display: 'flex',
        flexDirection: isCompact ? 'row' : 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: isCompact ? 0.5 : 1,
        position: 'relative',
        zIndex: 1,
        flex: isCompact ? 'none' : 1
      }}>
        {icon && !isCompact && (
          <Box sx={{
            fontSize: '1.5rem',
            mb: 0.5,
            filter: isSelected ? 'brightness(1.1)' : 'none',
            transition: 'all 0.3s ease',
            flexShrink: 0
          }}>
            {icon}
          </Box>
        )}

        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 0.5,
          mb: description && !isCompact ? 0.5 : 0,
          flexShrink: 0,
          flex: isCompact ? 1 : 'none',
          justifyContent: isCompact ? 'center' : 'center',
          height: isCompact ? '100%' : 'auto'
        }}>
          <Typography
            variant={isCompact ? "body2" : "subtitle1"}
            sx={{
              fontWeight: isCompact ? 500 : 600,
              color: isSelected ? 'var(--slate-blue)' : 'text.primary',
              fontSize: isCompact ? '0.75rem' : '1rem',
              lineHeight: isCompact ? 1.2 : 1.3,
              textAlign: 'center',
              transition: 'color 0.3s ease',
              wordBreak: 'break-word',
              flex: isCompact ? 1 : 'none'
            }}
          >
            {displayLabel}
          </Typography>

          {isSelected && (
            <CheckCircle
              sx={{
                color: 'var(--slate-blue)',
                fontSize: isCompact ? 14 : 18,
                flexShrink: 0,
                filter: 'drop-shadow(0 2px 4px rgba(94, 96, 206, 0.3))',
                animation: 'fadeIn 0.3s ease'
              }}
            />
          )}
        </Box>

        {description && !isCompact && (
          <Typography
            variant="body2"
            sx={{
              color: isSelected ? 'rgba(94, 96, 206, 0.8)' : 'text.secondary',
              fontSize: '0.8rem',
              lineHeight: 1.4,
              transition: 'color 0.3s ease',
              textAlign: 'center',
              wordBreak: 'break-word',
              flex: 1,
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {description}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
}
