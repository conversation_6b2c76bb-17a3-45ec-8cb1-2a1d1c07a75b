import { Box, FormControl, Select, MenuItem, Typography } from '@mui/material';
import { Work, ExpandMore } from '@mui/icons-material';
import { professionalRoles } from '@/config/constant';
import { PageHeader } from '@/components/ui';

interface StepProfessionalFocusProps {
  role?: string;
  onRoleChange: (role: string) => void;
}

export default function StepProfessionalFocus({
  role,
  onRoleChange
}: StepProfessionalFocusProps) {
  const selectedRole = professionalRoles.find(r => r.id === role);

  return (
    <Box>
      <PageHeader
        icon={<Work sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What's your professional role?"
        subtitle="Help us understand your expertise"
        description="This helps us show you the most relevant content and connections"
      />

      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
        <FormControl sx={{ minWidth: 400, maxWidth: 500 }}>
          <Select
            value={role || ''}
            onChange={(e) => onRoleChange(e.target.value)}
            displayEmpty
            IconComponent={ExpandMore}
            sx={{
              height: 64,
              borderRadius: 4,
              backgroundColor: 'white',
              fontSize: '1.1rem',
              fontWeight: 500,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.05)',
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: '#e0e0e0',
                borderWidth: 2,
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: 'var(--slate-blue)',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: 'var(--slate-blue)',
                borderWidth: 2,
              },
              '& .MuiSelect-select': {
                padding: '18px 16px',
                display: 'flex',
                alignItems: 'center',
              },
              '& .MuiSelect-icon': {
                color: 'var(--slate-blue)',
                fontSize: '1.5rem',
              }
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: 300,
                  marginTop: 1,
                  borderRadius: 3,
                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08)',
                  '& .MuiMenuItem-root': {
                    padding: '12px 16px',
                    minHeight: 48,
                    fontSize: '1rem',
                    '&:hover': {
                      backgroundColor: 'rgba(94, 96, 206, 0.08)',
                    },
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(94, 96, 206, 0.12)',
                      '&:hover': {
                        backgroundColor: 'rgba(94, 96, 206, 0.16)',
                      }
                    }
                  }
                }
              }
            }}
          >
            <MenuItem value="" disabled>
              <Typography sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                Select your professional role
              </Typography>
            </MenuItem>
            {professionalRoles.map((roleOption) => (
              <MenuItem key={roleOption.id} value={roleOption.id}>
                <Box>
                  <Typography sx={{ fontWeight: 500, color: 'text.primary' }}>
                    {roleOption.label}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.85rem' }}>
                    {roleOption.description}
                  </Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>

      {selectedRole && (
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 3
        }}>
          <Box sx={{
            padding: 3,
            borderRadius: 4,
            backgroundColor: 'rgba(94, 96, 206, 0.05)',
            border: '1px solid rgba(94, 96, 206, 0.2)',
            maxWidth: 400,
            textAlign: 'center'
          }}>
            <Typography variant="h6" sx={{
              color: 'var(--slate-blue)',
              fontWeight: 600,
              mb: 1
            }}>
              {selectedRole.label}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {selectedRole.description}
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
} 