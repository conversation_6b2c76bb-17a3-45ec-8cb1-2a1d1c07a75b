import { Button } from '@mui/material';
import { Google } from '@mui/icons-material';

interface GoogleSignInButtonProps {
  onClick: () => void;
  disabled?: boolean;
  text?: string;
}

export default function GoogleSignInButton({ 
  onClick, 
  disabled = false, 
  text = "Sign in with <PERSON>" 
}: GoogleSignInButtonProps) {
  return (
    <Button
      variant="outlined"
      fullWidth
      size="large"
      onClick={onClick}
      disabled={disabled}
      startIcon={<Google />}
      sx={{
        mb: 2,
        py: 1.5,
        textTransform: 'none',
        fontSize: '1rem',
        fontWeight: 600
      }}
    >
      {text}
    </Button>
  );
}
