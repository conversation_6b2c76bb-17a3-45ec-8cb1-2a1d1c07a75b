generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id          String   @id @default(uuid())
  createdAt   DateTime @default(now())
  userName    String   @unique
  isOnboarded Boolean  @default(false)

  updatedAt DateTime?
  bio       String?
  role      String?
  company   String?
  fullName  String?

  comments             Comment[]
  posts                Post[]
  reactions            PostReaction[]
  interestedCategories Category[]     @relation("UserToCategory")

  // Onboarding fields
  userType         String? // "founder" | "viewer" | "investor" | "other"
  experienceLevel  String? // "beginner" | "intermediate" | "experienced" | "expert"
  companyStage     String? // "idea" | "mvp" | "early" | "growth" | "established"
  companySize      String? // "solo" | "2-10" | "11-50" | "51-200" | "200+"
  workType         String? // "individual" | "team" | "freelance" | "agency"
  goals            String[] // Array of goals like ["networking", "feedback", "funding", "hiring"]
  referralSource   String? // "twitter" | "linkedin" | "google" | "friend" | "other"
  profileCompleted Int      @default(0) // Percentage of profile completion
}

enum PostStatus {
  IDEA
  DEVELOPMENT
  LAUNCHED
}

model Post {
  id           String         @id @default(uuid())
  title        String
  description  String
  body         String
  userId       String
  status       PostStatus
  thumbnailUrl String?
  categoryId   String
  website      String
  isPublished  Boolean        @default(true)
  isPinned     Boolean        @default(false)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  comments     Comment[]
  category     Category       @relation(fields: [categoryId], references: [id])
  user         User           @relation(fields: [userId], references: [id])
  tags         PostTag[]
  reactions    PostReaction[]
}

model Tag {
  id    String    @id @default(uuid())
  name  String
  color String
  posts PostTag[]
}

model PostTag {
  id     String @id @default(uuid())
  postId String
  tagId  String
  post   Post   @relation(fields: [postId], references: [id])
  tag    Tag    @relation(fields: [tagId], references: [id])
}

model Category {
  id    String @id @default(uuid())
  name  String
  color String
  posts Post[]
  users User[] @relation("UserToCategory")
}

model Comment {
  id        String    @id @default(uuid())
  content   String
  postId    String
  userId    String
  parentId  String?
  createdAt DateTime
  parent    Comment?  @relation("CommentToComment", fields: [parentId], references: [id])
  replies   Comment[] @relation("CommentToComment")
  post      Post      @relation(fields: [postId], references: [id])
  user      User      @relation(fields: [userId], references: [id])
}

model ReactionType {
  id        String         @id @default(uuid())
  name      String         @unique // e.g., "like", "love", "laugh", "angry", "sad"
  emoji     String // e.g., "👍", "❤️", "😂", "😠", "😢"
  reactions PostReaction[]
}

model PostReaction {
  id String @id @default(uuid())

  postId         String
  userId         String
  reactionTypeId String

  post         Post         @relation(fields: [postId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  reactionType ReactionType @relation(fields: [reactionTypeId], references: [id])

  // Ensure a user can only have one reaction per post
  @@unique([postId, userId])
}
