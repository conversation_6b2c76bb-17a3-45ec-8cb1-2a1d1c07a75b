'use client';

import { useState } from 'react';
import { resetPassword, checkIfEmailExisted } from '@/lib/auth';
import { handleAuthError } from '@/lib/error-handler/auth-error';
import { useAuthRedirect } from '@/hooks/useAuth';
import {
  TextField,
  Box,
  InputAdornment,
  Alert
} from "@mui/material";
import { Email, CheckCircle } from "@mui/icons-material";
import {
  AuthLayout,
  AuthNavigation,
  AuthPanel,
  AuthHeader,
  AuthButton
} from '@/components/auth';

export default function ResetPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { isLoading: isCheckingAuth } = useAuthRedirect();

  if (isCheckingAuth) {
    return (
      <AuthLayout>
        <AuthPanel>
          <div className="flex justify-center items-center py-8">
            <div className="text-center">Loading...</div>
          </div>
        </AuthPanel>
      </AuthLayout>
    );
  }

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const emailExists = await checkIfEmailExisted(email);
      if (!emailExists) {
        setError('No account found with this email address. Please check your email or create a new account.');
        setIsLoading(false);
        return;
      }

      await resetPassword(email);
      setIsSubmitted(true);
    } catch (error: any) {
      setError(handleAuthError(error, 'RESET_PASSWORD'));
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <AuthLayout>
        <AuthPanel maxWidth={600}>
          <AuthHeader
            icon={<CheckCircle sx={{ fontSize: 64, color: 'var(--slate-blue)' }} />}
            title="Check your email"
            subtitle="We've sent password reset instructions to your email address. Please check your inbox."
          />
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Box
              component="a"
              href="/sign-in"
              sx={{
                display: 'inline-block',
                textDecoration: 'none',
                py: 1.5,
                px: 4,
                border: '1px solid var(--slate-blue)',
                borderRadius: 1,
                color: 'var(--slate-blue)',
                fontWeight: 600,
                fontSize: '1rem',
                width: '100%',
                textAlign: 'center',
                '&:hover': {
                  borderColor: 'var(--grape)',
                  backgroundColor: 'rgba(94, 96, 206, 0.05)',
                }
              }}
            >
              Back to Sign In
            </Box>
          </Box>
        </AuthPanel>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <AuthNavigation
        text="Remember your password?"
        linkText="Sign in"
        linkHref="/sign-in"
      />

      <AuthPanel>
        <AuthHeader title="Forgot your password?" />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleResetPassword}>
          <TextField
            fullWidth
            required
            type="email"
            label="Email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Email />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
          />

          <AuthButton
            type="submit"
            fullWidth
            loading={isLoading}
            loadingText="Sending..."
          >
            Send me the link
          </AuthButton>
        </Box>
      </AuthPanel>
    </AuthLayout>
  );
}
