import { useState, useCallback } from 'react';
import { getData, postData, patchData, deleteData } from '@/lib/api';
import { 
  PostWithDetails, 
  PostWithBasicDetails, 
  CreatePostData, 
  UpdatePostData,
  PostStatus 
} from '@/config/type';

export function usePosts() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get all posts
  const getAllPosts = useCallback(async (): Promise<PostWithBasicDetails[]> => {
    setLoading(true);
    setError(null);
    try {
      const posts = await getData<PostWithBasicDetails[]>('/api/posts', { requiresAuth: false });
      return posts;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch posts';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get post by ID
  const getPostById = useCallback(async (id: string): Promise<PostWithDetails> => {
    setLoading(true);
    setError(null);
    try {
      const post = await getData<PostWithDetails>(`/api/posts/${id}`, { requiresAuth: false });
      return post;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch post';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new post
  const createPost = useCallback(async (data: CreatePostData): Promise<PostWithBasicDetails> => {
    setLoading(true);
    setError(null);
    try {
      const newPost = await postData<PostWithBasicDetails, CreatePostData>('/api/posts', data, { requiresAuth: false });
      return newPost;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create post';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update a post
  const updatePost = useCallback(async (id: string, data: UpdatePostData): Promise<PostWithBasicDetails> => {
    setLoading(true);
    setError(null);
    try {
      const updatedPost = await patchData<PostWithBasicDetails, UpdatePostData>(`/api/posts/${id}`, data, { requiresAuth: false });
      return updatedPost;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update post';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a post
  const deletePost = useCallback(async (id: string): Promise<{ message: string }> => {
    setLoading(true);
    setError(null);
    try {
      const result = await deleteData<{ message: string }>(`/api/posts/${id}`, { requiresAuth: false });
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete post';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    // State
    loading,
    error,
    
    // Actions
    getAllPosts,
    getPostById,
    createPost,
    updatePost,
    deletePost,
  };
} 