@import "tailwindcss";

@layer base {
  :root {
    /* Brand Colors */
    --french-violet: #7400b8;
    --grape: #6930c3;
    --slate-blue: #5e60ce;
    --united-nations-blue: #5390d9;
    --picton-blue: #4ea8de;
    --aero: #48bfe3;
    --sky-blue: #56cfe1;
    --tiffany-blue: #64dfdf;
    --turquoise: #72efdd;
    --aquamarine: #80ffdb;


  }

  html {
    scroll-behavior: smooth;
  }

  body {
    min-height: 100vh;
  }
}

@layer components {


  /* Legacy Components */
  .gradient-button {
    background: linear-gradient(to right,
      var(--french-violet),
      var(--grape),
      var(--slate-blue),
      var(--united-nations-blue),
      var(--aero)
    );
    color: white;
    box-shadow: 0 25px 50px -12px rgb(124 58 237 / 0.3);
    transition: all 0.2s ease;
    border-radius: 1rem;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.125rem;
  }

  .input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    border: 2px solid rgb(124 58 237 / 0.2);
    background: rgb(124 58 237 / 0.03);
    transition: all 0.2s ease;
    color: #6930c3;
  }

  .input-field:focus {
    outline: none;
    border-color: var(--french-violet);
    background: white;
  }

  .provider-google {
    background: linear-gradient(to bottom right, #5390d9, #4ea8de);
  }

  .provider-linkedin {
    background: linear-gradient(to bottom right, #6930c3, #8655d5);
  }

  .provider-facebook {
    background: linear-gradient(to bottom right, #5e60ce, #7f81d8);
  }

  .provider-twitter {
    background: linear-gradient(to bottom right, #48bfe3, #6ecce8);
  }

  .provider-default {
    background: linear-gradient(to bottom right, #7400b8, #9e00f9);
  }
}

/* Onboarding animations */
@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}