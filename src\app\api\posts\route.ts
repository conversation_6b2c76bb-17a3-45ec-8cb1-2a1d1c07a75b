import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/config/prisma-client";

// GET /api/posts - Get all posts
export async function GET(request: NextRequest) {
  try {
    const posts = await prisma.post.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: true,
        category: true,
        tags: {
          include: {
            tag: true
          }
        }
      }
    });
    return NextResponse.json(posts);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}

// POST /api/posts - Create a new post
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      title, 
      description, 
      body: postBody, 
      userId, 
      status,
      thumbnailUrl,
      categoryId,
      website,
      isPublished,
      isPinned 
    } = body;

    if (!title || !description || !postBody || !userId || !status || !categoryId || !website) {
      return NextResponse.json(
        { error: "Title, description, body, userId, status, categoryId, and website are required" },
        { status: 400 }
      );
    }

    const post = await prisma.post.create({
      data: {
        title,
        description,
        body: postBody,
        userId,
        status,
        thumbnailUrl,
        categoryId,
        website,
        isPublished: isPublished ?? true,
        isPinned: isPinned ?? false,
      },
      include: {
        user: true,
        category: true,
        tags: {
          include: {
            tag: true
          }
        }
      }
    });

    return NextResponse.json(post, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to create post" },
      { status: 500 }
    );
  }
} 