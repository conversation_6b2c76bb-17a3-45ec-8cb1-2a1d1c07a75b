import { Box } from '@mui/material';
import { Work } from '@mui/icons-material';
import { experienceLevels } from '@/config/constant';
import { SelectionCard, PageHeader } from '@/components/ui';

interface StepExperienceProps {
  userType?: string;
  experienceLevel?: string;
  onExperienceLevelChange: (experienceLevel: string) => void;
}

export default function StepExperience({
  userType,
  experienceLevel,
  onExperienceLevelChange
}: StepExperienceProps) {
  const selectedUserType = userType as keyof typeof experienceLevels || 'founder';
  const availableExperienceLevels = experienceLevels[selectedUserType] || experienceLevels.founder;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <PageHeader
        icon={<Work sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What's your experience level?"
        subtitle="Help us understand your background"
        description="This helps us show you the most relevant content and connections"
      />

      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2,
        width: '70%'
      }}>
        {availableExperienceLevels.map((level) => (
          <SelectionCard
            key={level.id}
            id={level.id}
            label={level.label}
            description={level.description}
            isSelected={experienceLevel === level.id}
            onClick={onExperienceLevelChange}
          />
        ))}
      </Box>
    </Box>
  );
} 