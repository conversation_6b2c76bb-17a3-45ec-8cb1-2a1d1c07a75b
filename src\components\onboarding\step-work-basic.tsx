import { Box, TextField } from '@mui/material';
import { Business, Groups } from '@mui/icons-material';
import { workTypes } from '@/config/constant';
import { SelectionCard, PageHeader } from '@/components/ui';

interface StepWorkBasicProps {
  userType?: string;
  company?: string;
  workType?: string;
  onCompanyChange: (company: string) => void;
  onWorkTypeChange: (type: string) => void;
}

export default function StepWorkBasic({
  userType,
  company,
  workType,
  onCompanyChange,
  onWorkTypeChange
}: StepWorkBasicProps) {
  const isFounder = userType === 'founder';

  if (isFounder) {
    return (
      <Box>
        <PageHeader
          icon={<Business sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
          title="What's your company name?"
          subtitle="Tell us about your startup"
          description="Help us connect you with relevant resources and people"
        />

        <Box sx={{ display: 'flex', justifyContent: 'center', px: 2 }}>
          <TextField
            fullWidth
            placeholder="Enter your company name (optional)"
            value={company || ''}
            onChange={(e) => onCompanyChange(e.target.value)}
            sx={{
              maxWidth: 500,
              '& .MuiOutlinedInput-root': {
                borderRadius: 4,
                height: 64,
                fontSize: '1.1rem',
                background: 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
                '&:hover fieldset': {
                  borderColor: 'var(--slate-blue)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'var(--slate-blue)',
                  borderWidth: 2,
                },
              },
              '& .MuiInputBase-input': {
                textAlign: 'center',
                fontWeight: 500,
              }
            }}
          />
        </Box>
      </Box>
    );
  }

  // For non-founders
  return (
    <Box>
      <PageHeader
        icon={<Groups sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What's your work situation?"
        subtitle="Help us understand your professional context"
        description="This helps us personalize your startup discovery experience"
      />

      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2
      }}>
        {workTypes.map((type) => (
          <SelectionCard
            key={type.id}
            id={type.id}
            label={type.label}
            description={type.description}
            isSelected={workType === type.id}
            onClick={onWorkTypeChange}
          />
        ))}
      </Box>
    </Box>
  );
} 