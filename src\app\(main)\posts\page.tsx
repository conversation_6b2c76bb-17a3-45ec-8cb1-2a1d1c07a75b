import PostCardList from "@/components/post-listing/post-card-list";
import { PostStatus, PostWithBasicDetails } from "@/config/type";


// This is just a mock data for testing
export const mockPost: PostWithBasicDetails[] = [{
  id: "1",
  title: "Sample Post Title",
  description: "This is a sample post content for testing.",
  thumbnailUrl: "https://cdn-icons-png.flaticon.com/512/5968/5968672.png",
  status: PostStatus.LAUNCHED,
  isPinned: true,
  createdAt: new Date(),
  categoryId: "category-1",
  category: {
    id: "category-1",
    name: "<PERSON>",
    color: "red",
  },

  tags: [
    {
      tag: {
        id: "tag-1",
        name: "vcl",
        color: "yellow",
      },
    },
    {
      tag: {
        id: "tag-2",
        name: "Du ma",
        color: "yellow",
      },
    },
  ],

  reactions: [
    {
      reactionType: {
        id: "like-1",
        name: "like",
        emoji: "👍",
      },
    },
    {
      reactionType: {
        id: "love-1",
        name: "love",
        emoji: "❤️",
      },
    },
  ],
}];


export default function Home() {
  return (
    <main className="flex items-center justify-center h-full w-full">
      <PostCardList posts={mockPost} />
    </main>
  );
}
