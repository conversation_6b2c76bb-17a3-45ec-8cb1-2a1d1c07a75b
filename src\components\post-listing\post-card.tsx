'use client';

import React from 'react';
import { Chip } from '@mui/material';
import { useRouter } from 'next/navigation';
import type { PostStatus, PostWithBasicDetails } from '@/config/type';

interface PostCardProps {
  post: PostWithBasicDetails;
  onReact?: (emoji: string) => void;
}

const statusColorMap: Record<PostStatus, 'default' | 'info' | 'success'> = {
  IDEA: 'default',
  DEVELOPMENT: 'info',
  LAUNCHED: 'success',
};

const PostCard: React.FC<PostCardProps> = ({
  post,
  onReact,
}) => {
  const router = useRouter();

  const handleCardClick = () => {
    router.push(`/posts/${post.id}`);
  };

  const handleReactionClick = (
    e: React.MouseEvent<HTMLDivElement>,
    emoji: string
  ) => {
    e.stopPropagation(); // prevent card navigation
    onReact?.(emoji);
  };

  // Group reaction by types and count them
  // Currently all post details are being fetched in 1 query and then grouped in memory
  // When we have more users, we can split into 3 queries to do the grouping and count in DB
  const groupedReactions = post.reactions.reduce((result, { reactionType }) => {
    const key = reactionType.id;
    if (!result[key]) {
      result[key] = { ...reactionType, count: 1 };
    } else {
      result[key].count += 1;
    }
    return result;
  }, {} as Record<string, { emoji: string; name: string; count: number }>);

  return (
    <div
      onClick={handleCardClick}
      className="max-w-xl w-full rounded-lg p-4 bg-white shadow-sm hover:shadow-md hover:bg-gray-50 transition cursor-pointer"
    >
      <div className="flex gap-4">
        {/* Icon */}
        <div className="w-16 flex-shrink-0">
          <img
            src={post.thumbnailUrl ?? "./placeholder.png"}
            alt="Post Icon"
            className="w-16 h-full object-contain"
          />
        </div>

        {/* Content */}
        <div className="flex flex-col flex-1 gap-2">
          {/* Title + Status */}
          <div className="flex items-center gap-2 flex-wrap">
            <h2 className="text-lg font-semibold">{post.title}</h2>
            <Chip
              label={post.status.charAt(0) + post.status.slice(1).toLowerCase()}
              color={statusColorMap[post.status]}
              size="small"
            />
          </div>

          {/* Description */}
          <p className="text-sm text-gray-600">{post.description}</p>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            {post.tags.map((postTag, index) => (
              <Chip
                key={index}
                label={postTag.tag.name}
                size="small"
                variant="outlined"
              />
            ))}
          </div>

          {/* Reactions */}
          <div className="flex flex-wrap gap-2 mt-2">
            {Object.values(groupedReactions).map((reaction, index) => (
              <div
                key={index}
                title={reaction.name}
                onClick={(e) => handleReactionClick(e, reaction.emoji)}
                className="flex items-center px-3 py-1 border border-gray-300 rounded-xl text-sm cursor-pointer hover:bg-gray-200 transition"
              >
                <span className="mr-1">{reaction.emoji}</span>
                <span>{reaction.count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostCard;