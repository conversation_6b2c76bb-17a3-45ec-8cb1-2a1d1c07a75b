import { prisma } from '@/config/prisma-client';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();
    
    const numUsers = await prisma.user.count({
      where: {
        userName: email
      }
    });

    return NextResponse.json({ exists: numUsers > 0 });
  } catch (error) {
    return NextResponse.json({ exists: false }, { status: 500 });
  }
}
