import { Box, Chip } from '@mui/material';
import { Flag } from '@mui/icons-material';
import { platformGoals } from '@/config/constant';
import { SelectionCard, PageHeader } from '@/components/ui';

interface StepPlatformGoalsProps {
  goals: string[];
  onGoalsChange: (goals: string[]) => void;
}

export default function StepPlatformGoals({
  goals,
  onGoalsChange
}: StepPlatformGoalsProps) {
  const handleGoalToggle = (goalId: string) => {
    const updatedGoals = goals.includes(goalId)
      ? goals.filter(g => g !== goalId)
      : [...goals, goalId];
    onGoalsChange(updatedGoals);
  };

  return (
    <Box>
      <PageHeader
        icon={<Flag sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What do you hope to achieve on Nexus?"
        subtitle="Select all that apply"
        description="This helps us personalize your experience and show you the most relevant content"
      />

      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
        <Chip
          label={`${goals.length} selected`}
          color={goals.length > 0 ? 'primary' : 'default'}
          sx={{
            fontWeight: 600,
            backgroundColor: goals.length > 0 ? 'var(--slate-blue)' : undefined,
            fontSize: '0.9rem',
            height: 32
          }}
        />
      </Box>

      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2
      }}>
        {platformGoals.map((goal) => (
          <SelectionCard
            key={goal.id}
            id={goal.id}
            label={goal.label}
            description={goal.description}
            isSelected={goals.includes(goal.id)}
            onClick={handleGoalToggle}
          />
        ))}
      </Box>
    </Box>
  );
} 