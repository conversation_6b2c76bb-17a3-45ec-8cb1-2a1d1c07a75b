import { Prisma, PostStatus } from '@prisma/client';

// Export the enum for use in components
export { PostStatus };

// Use Prisma generated types with relations
export type User = Prisma.UserGetPayload<{}>;

export type Category = Prisma.CategoryGetPayload<{}>;

export type Tag = Prisma.TagGetPayload<{}>;

export type PostTag = Prisma.PostTagGetPayload<{
  include: { tag: true };
}>;

export type Comment = Prisma.CommentGetPayload<{
  include: { user: true };
}>;

export type ReactionType = Prisma.ReactionTypeGetPayload<{}>;

export type PostReaction = Prisma.PostReactionGetPayload<{
  include: { 
    user: true;
    reactionType: true;
  };
}>;

// Post with all possible relations (for detailed view)
export type PostWithDetails = Prisma.PostGetPayload<{
  include: {
    user: true;
    category: true;
    tags: {
      include: {
        tag: true;
      };
    };
    comments: {
      include: {
        user: true;
      };
    };
    reactions: {
      include: {
        user: true;
        reactionType: true;
      };
    };
  };
}>;

// Post with basic relations (for list view)
export type PostWithBasicDetails = Prisma.PostGetPayload<{
  select: {
    id: true;
    title: true;
    description: true;
    status: true;
    createdAt: true;
    thumbnailUrl: true;
    categoryId: true;
    category: true;
    isPinned: true;
    tags: {
      select: {
        tag: true;
      };
    };
    reactions: {
      select: {
        reactionType: true;
      };
    };
  };
}>;

