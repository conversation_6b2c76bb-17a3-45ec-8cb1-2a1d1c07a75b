import { baseUrl } from '@/config/constant';
import { createClient } from '@/config/supabase/supabase-client';

const supabase = createClient()

const signInWithGoogle = async () => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${baseUrl}/auth/callback`,
    },
  });

  if (error) {
    throw error;
  }

  return data;
};

const signUpWithEmail = async (email: string, password: string, fullName: string) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${baseUrl}/auth/callback`,
      data: {
        email: email,
        full_name: fullName
      }
    },
  });

  if (error) {
    throw error;
  }

  return data;
};

const signInWithEmail = async (email: string, password: string) => {
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    throw error;
  }
};

const resetPassword = async (email: string) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: baseUrl,
  });

  if (error) {
    throw error;
  }
};

const getSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    throw error;
  }

  return session;
};

const signOut = async () => {
  const { error } = await supabase.auth.signOut();

  if (error) {
    throw error;
  }
};

const checkIfEmailExisted = async (email: string) => {
  try {
    const response = await fetch('/api/auth/check-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();
    return data.exists;
  } catch (error) {
    return false;
  }
};

export {
  signInWithGoogle,
  signUpWithEmail,
  signInWithEmail,
  resetPassword,
  getSession,
  signOut,
  checkIfEmailExisted
};
