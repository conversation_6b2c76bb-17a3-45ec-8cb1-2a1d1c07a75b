import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { getSession } from '@/lib/auth';

export function useAuthRedirect() {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const session = await getSession();
        if (session?.user) {
          // User is authenticated, redirect to main page
          router.replace('/');
          return;
        }
      } catch (error) {
        // User is not authenticated, stay on auth page
        console.log('No active session');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  return { isLoading };
}
