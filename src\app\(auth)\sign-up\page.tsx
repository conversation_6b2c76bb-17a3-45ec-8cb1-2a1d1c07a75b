'use client';

import { signInWithGoogle, signUpWithEmail, checkIfEmailExisted } from '@/lib/auth';
import { handleAuthError } from '@/lib/error-handler/auth-error';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthRedirect } from '@/hooks/useAuth';
import {
  TextField,
  Box,
  InputAdornment,
  Alert,
  Divider,
  Typography
} from "@mui/material";
import { Email, Lock, Person } from "@mui/icons-material";
import {
  AuthLayout,
  AuthNavigation,
  AuthPanel,
  AuthHeader,
  GoogleSignInButton,
  AuthButton
} from '@/components/auth';

export default function SignUp() {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { isLoading: isCheckingAuth } = useAuthRedirect();

  if (isCheckingAuth) {
    return (
      <AuthLayout>
        <AuthPanel>
          <div className="flex justify-center items-center py-8">
            <div className="text-center">Loading...</div>
          </div>
        </AuthPanel>
      </AuthLayout>
    );
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      // Check if email already exists
      const emailExists = await checkIfEmailExisted(email);
      if (emailExists) {
        setError('This email is already registered. Please sign in instead or use a different email address.');
        setIsLoading(false);
        return;
      }

      await signUpWithEmail(email, password, fullName);
      router.push('/verification-sent');
    } catch (error: any) {
      setError(handleAuthError(error, 'SIGN_UP'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <AuthNavigation
        text="Already have an account?"
        linkText="Login"
        linkHref="/sign-in"
      />

      <AuthPanel>
        <AuthHeader title="Seconds to sign up!" />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <GoogleSignInButton
          onClick={async () => {
            try {
              setError('');
              await signInWithGoogle();
            } catch (error: any) {
              setError(error?.message || 'Failed to sign up with Google. Please try again.');
            }
          }}
          disabled={isLoading}
        />

        <Divider sx={{ my: 2 }}>
          <Typography variant="body2" color="text.secondary">
            or
          </Typography>
        </Divider>

          <Box component="form" onSubmit={handleSignUp} sx={{ mt: 1 }}>
            <TextField
              fullWidth
              required
              type="text"
              label="Full Name"
              placeholder="John Doe"
              value={fullName}
              onChange={e => setFullName(e.target.value)}
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              required
              type="email"
              label="Work Email"
              placeholder="<EMAIL>"
              value={email}
              onChange={e => setEmail(e.target.value)}
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Email />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              required
              type="password"
              label="Choose Password"
              placeholder="Minimum 6 characters"
              value={password}
              onChange={e => setPassword(e.target.value)}
              margin="normal"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 3 }}
            />

          <AuthButton
            type="submit"
            fullWidth
            loading={isLoading}
            loadingText="Creating account..."
          >
            Play with Nexus
          </AuthButton>
        </Box>
      </AuthPanel>
    </AuthLayout>
  );
}

