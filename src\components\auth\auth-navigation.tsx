import Link from 'next/link';
import { Box, Typography, Button } from '@mui/material';

interface AuthNavigationProps {
  text: string;
  linkText: string;
  linkHref: string;
}

export default function AuthNavigation({ text, linkText, linkHref }: AuthNavigationProps) {
  return (
    <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', p: 2, gap: 2 }}>
      <Typography variant="body1" sx={{ color: 'white', fontWeight: 500 }}>
        {text}
      </Typography>
      <Button
        component={Link}
        href={linkHref}
        variant="contained"
        sx={{
          backgroundColor: 'white',
          color: 'var(--slate-blue)',
          fontWeight: 600,
          textTransform: 'none',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
          }
        }}
      >
        {linkText}
      </Button>
    </Box>
  );
}
