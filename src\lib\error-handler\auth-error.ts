type AuthErrorType = 'SIGN_IN' | 'SIGN_UP' | 'RESET_PASSWORD';

export function handleAuthError(error: any, type: AuthErrorType): string {
  const errorMessage = error?.message || 'An unexpected error occurred';

  // Common error messages
  if (errorMessage.includes('Unable to validate email address') || errorMessage.includes('invalid format')) {
    return 'Please enter a valid email address.';
  }
  if (errorMessage.includes('Failed to fetch') || errorMessage.includes('Network')) {
    return 'Network error. Please check your connection and try again.';
  }

  // Sign-in specific errors
  if (type === 'SIGN_IN') {
    if (errorMessage.includes('Invalid login credentials')) {
      return 'Invalid email or password. Please check your credentials and try again.';
    }
    if (errorMessage.includes('Email not confirmed')) {
      return 'Please check your email and click the confirmation link before signing in.';
    }
    if (errorMessage.includes('Too many requests')) {
      return 'Too many sign-in attempts. Please wait a moment before trying again.';
    }
  }

  // Sign-up specific errors
  if (type === 'SIGN_UP') {
    if (errorMessage.includes('Password should be at least')) {
      return 'Password must be at least 6 characters long.';
    }
    if (errorMessage.includes('User already registered')) {
      return 'This email is already registered. Please sign in instead or use a different email address.';
    }
  }

  // Reset password specific errors
  if (type === 'RESET_PASSWORD') {
    if (errorMessage.includes('User not found')) {
      return 'No account found with this email address.';
    }
  }

  // Default error message
  return errorMessage;
}
