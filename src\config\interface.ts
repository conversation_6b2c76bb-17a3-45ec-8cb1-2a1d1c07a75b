import { PostStatus } from "@/config/type";

// Input types for creating and updating posts
export interface CreatePostData {
  title: string;
  description: string;
  body: string;
  userId: string;
  status: PostStatus;
  thumbnailUrl?: string;
  categoryId: string; // Required now
  website: string; // Required now
  isPublished?: boolean;
  isPinned?: boolean;
}

export interface UpdatePostData {
  title?: string;
  description?: string;
  body?: string;
  status?: PostStatus;
  thumbnailUrl?: string;
  categoryId?: string;
  website?: string;
  isPublished?: boolean;
  isPinned?: boolean;
}

// Create and Update types for other entities
export interface CreateCategoryData {
  name: string;
  color: string;
}

export interface UpdateCategoryData {
  name?: string;
  color?: string;
}

export interface CreateTagData {
  name: string;
  color: string;
}

export interface UpdateTagData {
  name?: string;
  color?: string;
}

export interface CreateUserData {
  userName?: string;
  bio?: string;
  websiteUrl?: string;
  role?: string;
  company?: string;
  accountId?: string;
}

export interface UpdateUserData {
  userName?: string;
  bio?: string;
  websiteUrl?: string;
  role?: string;
  company?: string;
  accountId?: string;
}
