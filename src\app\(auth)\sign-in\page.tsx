"use client";

import { signInWithGoogle, signInWithEmail } from "@/lib/auth";
import { handleAuthError } from "@/lib/error-handler/auth-error";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useAuthRedirect } from "@/hooks/useAuth";
import {
  TextField,
  Box,
  InputAdornment,
  Alert,
  Divider,
  Typography
} from "@mui/material";
import { Email, Lock } from "@mui/icons-material";
import { appName } from "@/config/constant";
import {
  AuthNavigation,
  GoogleSignInButton
} from '@/components/auth';
import {
  AuthLayout,
  Panel,
  PageHeader,
  PrimaryButton
} from '@/components/ui';

export default function SignIn() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { isLoading: isCheckingAuth } = useAuthRedirect();

  if (isCheckingAuth) {
    return (
      <AuthLayout>
        <Panel>
          <div className="flex justify-center items-center py-8">
            <div className="text-center">Loading...</div>
          </div>
        </Panel>
      </AuthLayout>
    );
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      await signInWithEmail(email, password);
      router.push('/');
    } catch (error: any) {
      setError(handleAuthError(error, 'SIGN_IN'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthLayout>
      <AuthNavigation
        text="Don't have an account?"
        linkText="Sign up"
        linkHref="/sign-up"
      />

      <Panel>
        <PageHeader title={appName} />

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <GoogleSignInButton
          onClick={async () => {
            try {
              setError('');
              await signInWithGoogle();
            } catch (error: any) {
              setError(error?.message || 'Failed to sign in with Google. Please try again.');
            }
          }}
          disabled={isLoading}
        />

        <Divider sx={{ my: 2 }}>
          <Typography variant="body2" color="text.secondary">
            or
          </Typography>
        </Divider>

        <Box component="form" onSubmit={handleSignIn} sx={{ mt: 1 }}>
          <TextField
            fullWidth
            required
            type="email"
            label="Work Email"
            placeholder="Enter your work email"
            value={email}
            onChange={e => setEmail(e.target.value)}
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Email />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            required
            type="password"
            label="Password"
            placeholder="Enter password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            margin="normal"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 1 }}
          />

          <Box sx={{ textAlign: 'right', mb: 2 }}>
            <Box
              component="a"
              href="/reset-password"
              sx={{
                textDecoration: 'none',
                color: 'var(--slate-blue)',
                fontWeight: 600,
                fontSize: '0.875rem',
                '&:hover': { opacity: 0.8 }
              }}
            >
              Forgot Password?
            </Box>
          </Box>

          <PrimaryButton
            type="submit"
            fullWidth
            loading={isLoading}
            loadingText="Signing in..."
          >
            Log In
          </PrimaryButton>
        </Box>
      </Panel>
    </AuthLayout>
  );
}

