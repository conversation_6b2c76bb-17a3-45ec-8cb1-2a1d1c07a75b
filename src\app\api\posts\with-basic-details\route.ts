import { NextResponse } from "next/server";
import { prisma } from "@/config/prisma-client";

// GET /api/posts/with-basic-details - Get all PostWithBasicDetails
export async function GET() {
  try {
    const posts = await prisma.post.findMany({
      orderBy: {
        createdAt: 'desc'
      },
      select: {
          id: true,
          title: true,
          description: true,
          status: true,
          createdAt: true,
          thumbnailUrl: true,
          categoryId: true,
          category: true,
          isPinned: true,
          tags: {
            select: {
              tag: true
            }
          },
          reactions: {
            select: {
              reactionType: true
            }
          },
        },
    });
    return NextResponse.json(posts);
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}
