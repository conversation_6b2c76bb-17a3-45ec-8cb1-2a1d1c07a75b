import { ReactNode } from 'react';
import { Box, Paper } from '@mui/material';

interface AuthPanelProps {
  children: ReactNode;
  maxWidth?: number;
}

export default function AuthPanel({ children, maxWidth = 500 }: AuthPanelProps) {
  return (
    <Box 
      sx={{ 
        flex: 1, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        px: 1,
        pb: 4
      }}
    >
      <Paper 
        elevation={8}
        sx={{ 
          p: 4, 
          width: '100%', 
          maxWidth,
          borderRadius: 4,
          backgroundColor: 'white'
        }}
      >
        {children}
      </Paper>
    </Box>
  );
}
