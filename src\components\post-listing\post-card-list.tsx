'use client';

import React from 'react';
import PostCard from './post-card';
import type { PostWithBasicDetails } from '@/config/type';

interface PostCardListProps {
  posts: PostWithBasicDetails[];
}

const PostCardList: React.FC<PostCardListProps> = ({ posts }) => {
  const handleReact = (postId: string, emoji: string) => {
    // TODO: Add API request to record count of reactions
    console.log(`Reacted to post ${postId} with ${emoji}`);
  };

  return (
    <div className="space-y-4">
      {posts.map((post) => (
        <PostCard
          key={post.id}
          post={post}
          onReact={(emoji) => handleReact(post.id, emoji)}
        />
      ))}
    </div>
  );
};

export default PostCardList;