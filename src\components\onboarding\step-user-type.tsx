import { Box } from '@mui/material';
import { Person } from '@mui/icons-material';
import { userTypes } from '@/config/constant';
import OptionCard from './option-card';
import { AuthHeader } from '@/components/auth';

interface StepUserTypeProps {
  userType?: string;
  onUserTypeChange: (userType: string) => void;
}

export default function StepUserType({
  userType,
  onUserTypeChange
}: StepUserTypeProps) {
  return (
    <Box>
      <AuthHeader
        icon={<Person sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What brings you to Nexus?"
        subtitle="Help us personalize your experience"
        description="Tell us a bit about yourself and what you're looking to achieve"
      />

      {/* User Type Selection */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2
      }}>
        {userTypes.map((type) => (
          <OptionCard
            key={type.id}
            id={type.id}
            label={type.label}
            description={type.description}
            isSelected={userType === type.id}
            onClick={onUserTypeChange}
          />
        ))}
      </Box>
    </Box>
  );
} 