import { Box } from '@mui/material';
import { Person } from '@mui/icons-material';
import { userTypes } from '@/config/constant';
import { SelectionCard, PageHeader } from '@/components/ui';

interface StepUserTypeProps {
  userType?: string;
  onUserTypeChange: (userType: string) => void;
}

export default function StepUserType({
  userType,
  onUserTypeChange
}: StepUserTypeProps) {
  return (
    <Box>
      <PageHeader
        icon={<Person sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What brings you to Nexus?"
        subtitle="Help us personalize your experience"
        description="Tell us a bit about yourself and what you're looking to achieve"
      />

      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2
      }}>
        {userTypes.map((type) => (
          <SelectionCard
            key={type.id}
            id={type.id}
            label={type.label}
            description={type.description}
            isSelected={userType === type.id}
            onClick={onUserTypeChange}
          />
        ))}
      </Box>
    </Box>
  );
} 