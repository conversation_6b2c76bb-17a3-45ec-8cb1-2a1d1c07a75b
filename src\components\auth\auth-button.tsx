import { Button, ButtonProps } from '@mui/material';

interface AuthButtonProps extends Omit<ButtonProps, 'variant'> {
  variant?: 'primary' | 'secondary';
  loading?: boolean;
  loadingText?: string;
}

export default function AuthButton({
  variant = 'primary',
  loading = false,
  loadingText = 'Loading...',
  children,
  disabled,
  sx,
  ...props
}: AuthButtonProps) {
  const isPrimary = variant === 'primary';

  return (
    <Button
      variant="contained"
      size="large"
      disabled={disabled || loading}
      sx={{
        py: 1.5,
        px: isPrimary ? 6 : 3,
        background: isPrimary
          ? 'linear-gradient(135deg, var(--slate-blue) 0%, var(--grape) 100%)'
          : 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
        color: isPrimary ? 'white' : 'var(--slate-blue)',
        border: isPrimary ? 'none' : '2px solid var(--slate-blue)',
        boxShadow: isPrimary
          ? '0 4px 15px rgba(94, 96, 206, 0.4), 0 2px 8px rgba(0, 0, 0, 0.1)'
          : '0 2px 8px rgba(0, 0, 0, 0.08)',
        '&:hover': {
          background: isPrimary
            ? 'linear-gradient(135deg, var(--grape) 0%, var(--french-violet) 100%)'
            : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          boxShadow: isPrimary
            ? '0 6px 20px rgba(94, 96, 206, 0.5), 0 4px 12px rgba(0, 0, 0, 0.15)'
            : '0 4px 12px rgba(94, 96, 206, 0.2)',
          transform: 'translateY(-1px)',
        },
        '&:active': {
          transform: 'translateY(0px)',
          boxShadow: isPrimary
            ? '0 2px 8px rgba(94, 96, 206, 0.4)'
            : '0 1px 4px rgba(94, 96, 206, 0.2)',
        },
        '&:disabled': {
          background: 'rgba(0, 0, 0, 0.12)',
          color: 'rgba(0, 0, 0, 0.26)',
          boxShadow: 'none',
          transform: 'none',
        },
        textTransform: 'none',
        fontSize: '1.1rem',
        fontWeight: 600,
        borderRadius: 3,
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        position: 'relative',
        overflow: 'hidden',
        '&::before': isPrimary ? {
          content: '""',
          position: 'absolute',
          top: 0,
          left: '-100%',
          width: '100%',
          height: '100%',
          background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
          transition: 'left 0.5s',
        } : {},
        '&:hover::before': isPrimary ? {
          left: '100%',
        } : {},
        ...sx
      }}
      {...props}
    >
      {loading ? loadingText : children}
    </Button>
  );
}
