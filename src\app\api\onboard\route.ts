import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/config/prisma-client";
import { createClient } from "@/config/supabase/supabase-server";

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      userType,
      experienceLevel,
      company,
      companyStage,
      companySize,
      workType,
      role,
      goals,
      selectedIndustries
    } = body;

    // Validate required fields
    if (!selectedIndustries || !Array.isArray(selectedIndustries) || selectedIndustries.length === 0) {
      return NextResponse.json(
        { error: "At least one industry must be selected" },
        { status: 400 }
      );
    }

    if (!userType) {
      return NextResponse.json(
        { error: "User type is required" },
        { status: 400 }
      );
    }

    // Validate categories exist
    const categories = await prisma.tag.findMany({
      where: {
        name: {
          in: selectedIndustries
        }
      }
    });

    if (categories.length !== selectedIndustries.length) {
      return NextResponse.json(
        { error: "Some selected industries are invalid" },
        { status: 400 }
      );
    }

    // Calculate profile completion percentage
    let profileCompleted = 0;
    const maxFields = 9; // Updated to 9 fields (removed referralSource)
    
    if (userType) profileCompleted += 11; // ~11% per field (100/9)
    if (experienceLevel) profileCompleted += 11;
    if (company) profileCompleted += 11;
    if (companyStage) profileCompleted += 11;
    if (companySize) profileCompleted += 11;
    if (workType) profileCompleted += 11;
    if (role) profileCompleted += 11;
    if (goals && goals.length > 0) profileCompleted += 11;
    if (selectedIndustries.length > 0) profileCompleted += 12; // Make it add up to 100

    // Update user with all new fields
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        isOnboarded: true,
        updatedAt: new Date(),
        userType,
        experienceLevel,
        company: company || null,
        companyStage: companyStage || null,
        companySize: companySize || null,
        workType: workType || null,
        role: role || null,
        goals: goals || [],
        referralSource: null, // Explicitly set to null since discovery step is removed
        profileCompleted,
        interestedTags: {
          connect: categories.map(category => ({ id: category.id }))
        }
      },
      include: {
        interestedTags: true
      }
    });

    return NextResponse.json({
      message: "Onboarding completed successfully",
      user: updatedUser
    });
  } catch (error) {
    console.error('Onboarding error:', error);
    return NextResponse.json(
      { error: "Failed to complete onboarding" },
      { status: 500 }
    );
  }
}
