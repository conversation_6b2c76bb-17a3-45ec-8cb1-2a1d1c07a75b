'use client';

import { Typography, Box } from "@mui/material";
import { Email } from "@mui/icons-material";
import { AuthLayout, AuthPanel, AuthHeader } from '@/components/auth';

export default function VerificationSent() {
  return (
    <AuthLayout>
      <AuthPanel>
        <AuthHeader
          icon={<Email sx={{ fontSize: 64, color: 'var(--slate-blue)' }} />}
          title="Check your email"
          subtitle="We've sent a verification link to your email address. Please check your inbox and click the link to verify your account."
        />
        <Typography variant="body2" sx={{ color: 'text.secondary', textAlign: 'center', mt: 2 }}>
          Didn't receive the email? Check your spam folder or{' '}
          <Box
            component="a"
            href="/sign-up"
            sx={{
              textDecoration: 'none',
              color: 'var(--slate-blue)',
              fontWeight: 600,
              '&:hover': { opacity: 0.8 }
            }}
          >
            try again
          </Box>
        </Typography>
      </AuthPanel>
    </AuthLayout>
  );
}
