import { Box } from '@mui/material';
import { Timeline } from '@mui/icons-material';
import { companyStages } from '@/config/constant';
import OptionCard from './option-card';
import { AuthHeader } from '@/components/auth';

interface StepCompanyStageProps {
  userType?: string;
  companyStage?: string;
  onCompanyStageChange: (stage: string) => void;
}

export default function StepCompanyStage({
  userType,
  companyStage,
  onCompanyStageChange
}: StepCompanyStageProps) {
  const isFounder = userType === 'founder';

  if (!isFounder) {
    // Skip this step for non-founders
    return (
      <Box>
        <AuthHeader
          icon={<Timeline sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
          title="You're all set for this step!"
          subtitle="Let's continue with team information"
          description="This step is specifically for founders to tell us about their company stage"
        />
      </Box>
    );
  }

  return (
    <Box>
      <AuthHeader
        icon={<Timeline sx={{ fontSize: 48, color: 'var(--slate-blue)' }} />}
        title="What stage is your company at?"
        subtitle="Help us understand your startup journey"
        description="This helps us connect you with relevant resources and similar-stage founders"
      />

      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: 2,
        px: 2
      }}>
        {companyStages.map((stage) => (
          <OptionCard
            key={stage.id}
            id={stage.id}
            label={stage.label}
            description={stage.description}
            isSelected={companyStage === stage.id}
            onClick={onCompanyStageChange}
          />
        ))}
      </Box>
    </Box>
  );
} 