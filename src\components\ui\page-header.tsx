import { ReactNode } from 'react';
import { Box, Typography } from '@mui/material';

interface PageHeaderProps {
  icon?: ReactNode;
  title: string;
  subtitle?: string;
  description?: string;
}

export default function PageHeader({ icon, title, subtitle, description }: PageHeaderProps) {
  return (
    <Box sx={{ textAlign: 'center', mb: 4 }}>
      {icon && (
        <Box sx={{ mb: 2 }}>
          {icon}
        </Box>
      )}
      <Typography 
        variant="h3" 
        component="h1" 
        sx={{ 
          fontWeight: 'bold', 
          mb: 2,
          fontSize: { xs: '2rem', sm: '2.5rem' },
          color: 'text.primary'
        }}
      >
        {title}
      </Typography>
      {subtitle && (
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 1 }}>
          {subtitle}
        </Typography>
      )}
      {description && (
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          {description}
        </Typography>
      )}
    </Box>
  );
}
