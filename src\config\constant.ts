export const baseUrl = "http://localhost:3000";

export const appName = "Nexus";

export const industries: string[] = [
  "SaaS Software",
  "E-commerce Retail",
  "FinTech",
  "HealthTech",
  "EdTech",
  "AI Machine Learning",
  "Mobile Apps",
  "Web Development",
  "Marketplace Platform",
  "Social Media",
  "Gaming Entertainment",
  "Cybersecurity",
  "Data Analytics",
  "Developer Tools",
  "B2B Tools",
  "Hardware IoT",
  "Blockchain Crypto",
  "CleanTech Green Energy",
  "Travel Hospitality",
  "Real Estate PropTech",
  "Fashion Lifestyle",
  "Creator Economy",
  "Logistics Supply Chain",
  "Automotive Transportation",
  "Biotechnology",
  "VR AR",
  "Robotics Automation",
  "Other"
];

// Onboarding options
export const userTypes = [
  { id: "founder", label: "I'm a founder", description: "Building or planning to build a startup" },
  { id: "viewer", label: "I'm discovering startups", description: "Interested in following and supporting startups" },
  { id: "other", label: "Other", description: "None of the above describes me" }
];

export const experienceLevels = {
  founder: [
    { id: "beginner", label: "First-time founder", description: "This is my first startup venture" },
    { id: "intermediate", label: "Some experience", description: "I've been involved in 1-2 startups before" },
    { id: "experienced", label: "Serial entrepreneur", description: "I've founded 3+ companies" },
    { id: "expert", label: "Experienced professional", description: "Transitioning from corporate to founding" }
  ],
  viewer: [
    { id: "beginner", label: "New to startups", description: "Just getting interested in the startup world" },
    { id: "intermediate", label: "Some knowledge", description: "I follow startups and understand the basics" },
    { id: "experienced", label: "Well-versed", description: "I have good knowledge of startup ecosystems" },
    { id: "expert", label: "Industry expert", description: "I work closely with startups professionally" }
  ],
  other: [
    { id: "beginner", label: "Beginner", description: "New to this space" },
    { id: "intermediate", label: "Some experience", description: "Have some background knowledge" },
    { id: "experienced", label: "Experienced", description: "Good understanding of the domain" }
  ]
};

export const companyStages = [
  { id: "idea", label: "Idea stage", description: "Still developing the concept" },
  { id: "mvp", label: "MVP/Early stage", description: "Building or testing initial product" },
  { id: "early", label: "Early growth", description: "Have product-market fit, scaling up" },
  { id: "growth", label: "Growth stage", description: "Established revenue, expanding market" },
  { id: "established", label: "Established", description: "Mature company with proven model" }
];

export const companySizes = [
  { id: "solo", label: "Just me", description: "Solo founder" },
  { id: "2-10", label: "2-10 people", description: "Small team" },
  { id: "11-50", label: "11-50 people", description: "Growing team" },
  { id: "51-200", label: "51-200 people", description: "Mid-size company" },
  { id: "200+", label: "200+ people", description: "Large organization" }
];

export const workTypes = [
  { id: "individual", label: "Individual contributor", description: "Working independently" },
  { id: "team", label: "Team member", description: "Part of a larger team" },
  { id: "freelance", label: "Freelancer/Consultant", description: "Independent contractor" },
  { id: "agency", label: "Agency/Service provider", description: "Part of an agency or service business" },
  { id: "student", label: "Student", description: "Currently studying" },
  { id: "between", label: "Between opportunities", description: "Looking for new opportunities" }
];

export const platformGoals = [
  { id: "feedback", label: "Get feedback", description: "Receive input on my startup or ideas" },
  { id: "networking", label: "Network", description: "Connect with other founders and professionals" },
  { id: "team", label: "Find team members", description: "Recruit co-founders or employees" },
  { id: "funding", label: "Seek funding", description: "Connect with potential investors" },
  { id: "discover", label: "Discover startups", description: "Find and follow interesting companies" },
  { id: "invest", label: "Investment opportunities", description: "Find startups to invest in" },
  { id: "knowledge", label: "Share knowledge", description: "Help others with my expertise" },
  { id: "learn", label: "Learn", description: "Stay updated on startup trends and insights" }
];

export const professionalRoles = [
  { id: "founder", label: "Founder / Entrepreneur", description: "Leading or building my own company" },
  { id: "software-engineer", label: "Software Engineer / Developer", description: "Building and maintaining software applications" },
  { id: "designer", label: "Designer", description: "Creating visual designs, UX/UI, or graphics" },
  { id: "product-manager", label: "Product Manager", description: "Managing product development and strategy" },
  { id: "marketing", label: "Marketing Professional", description: "Marketing, digital marketing, or content creation" },
  { id: "sales", label: "Sales Professional", description: "Sales, business development, or account management" },
  { id: "data-analyst", label: "Data Analyst / Scientist", description: "Working with data analysis or data science" },
  { id: "finance", label: "Finance / Accounting", description: "Financial planning, accounting, or investment" },
  { id: "hr-recruiter", label: "HR / Recruiting", description: "Human resources, recruiting, or talent management" },
  { id: "operations", label: "Operations Manager", description: "Managing business operations or logistics" },
  { id: "project-manager", label: "Project Manager", description: "Managing projects and coordinating teams" },
  { id: "customer-support", label: "Customer Support", description: "Customer service, support, or success" },
  { id: "business-analyst", label: "Business Analyst", description: "Analyzing business processes and requirements" },
  { id: "consultant", label: "Consultant / Advisor", description: "Providing professional consulting services" },
  { id: "teacher", label: "Teacher / Educator", description: "Teaching, training, or education" },
  { id: "healthcare", label: "Healthcare Professional", description: "Medical, nursing, or healthcare services" },
  { id: "legal", label: "Legal Professional", description: "Law, legal services, or compliance" },
  { id: "student", label: "Student", description: "Currently studying or in school" },
  { id: "freelancer", label: "Freelancer", description: "Working independently across various projects" },
  { id: "other", label: "Other", description: "My profession isn't listed here" }
];